use chrono::{
    NaiveDate,
    NaiveTime,
    Datelike,
};
use regex::Regex;


pub fn normalize_date(date: &str) -> String {
    // __HAS_TEST__

    // first try parsing 2-digit year format with manual fix
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%y") {
        let year = parsed.year();
        let fixed_year = if year < 100 {
            if year >= 70 { 1900 + year } else { 2000 + year }
        } else {
            year
        };

        if let Some(fixed_date) = NaiveDate::from_ymd_opt(fixed_year, parsed.month(), parsed.day()) {
            return fixed_date.format("%Y-%m-%d").to_string();
        }
    }

    // then try parsing 4-digit year normally
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%Y") {
        return parsed.format("%Y-%m-%d").to_string();
    }

    // if all formats fail,
    // return the original date string
    date.to_string()
}


pub fn normalize_dns_question_name(url: &str) -> String {
    // __HAS_TEST__

    let re = Regex::new(r"\([0-9]+\)").unwrap();
    let normalized = re.replace_all(url, ".");
    normalized.trim_matches('.').to_string()
}


pub fn normalize_time(time: &str) -> String {
    // __HAS_TEST__

    // try parsing with 12-hour format with AM/PM (%I:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%I:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // try parsing with 24-hour format with AM/PM (%H:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%H:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // if all formats fail,
    // return the original time string
    time.to_string()
}

// ...
